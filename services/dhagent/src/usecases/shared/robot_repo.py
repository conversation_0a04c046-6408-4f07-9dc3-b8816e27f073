"""Robot repository interfaces - Domain layer contracts."""

from abc import ABC, abstractmethod

from src.external.robot.model import (
    AddMembersRequest,
    AddMembersResponse,
    ConversationHistoryResponse,
    ConversationResponse,
    CreateConversationRequest,
    SendMessageRequest,
    SendMessageResponse,
    UpdateConversationRequest,
    UserCredentialsRequest,
    UserCredentialsResponse,
)


class RobotRepository(ABC):
    """
    Abstract repository for robot operations.

    This interface defines the contract for robot data access operations,
    following Clean Architecture principles by keeping the domain layer
    independent of external frameworks and infrastructure.
    """

    @abstractmethod
    async def get_user_credentials(
        self, request: UserCredentialsRequest, said_token: str | None = None
    ) -> UserCredentialsResponse:
        """
        Get user authorization credentials.

        Args:
            request: User credentials request
            said_token: Optional SAID token for Client-Assertion header

        Returns:
            User credentials response with service tokens

        Raises:
            RobotRepositoryError: If the operation fails
        """
        pass

    @abstractmethod
    async def create_conversation(self, request: CreateConversationRequest) -> ConversationResponse:
        """
        Create a new conversation.

        Args:
            request: Conversation creation request

        Returns:
            Created conversation response

        Raises:
            RobotRepositoryError: If the operation fails
        """
        pass

    @abstractmethod
    async def add_conversation_members(self, conversation_id: int, request: AddMembersRequest) -> AddMembersResponse:
        """
        Add members to an existing conversation.

        Args:
            conversation_id: Target conversation ID
            request: Members addition request

        Returns:
            Members addition response

        Raises:
            RobotRepositoryError: If the operation fails
        """
        pass

    @abstractmethod
    async def update_conversation(
        self, conversation_id: int, request: UpdateConversationRequest
    ) -> ConversationResponse:
        """
        Update conversation properties.

        Args:
            conversation_id: Target conversation ID
            request: Conversation update request

        Returns:
            Updated conversation response

        Raises:
            RobotRepositoryError: If the operation fails
        """
        pass

    @abstractmethod
    async def send_message(self, conversation_id: int, request: SendMessageRequest) -> SendMessageResponse:
        """
        Send a message to a conversation.

        Args:
            conversation_id: Target conversation ID
            request: Message sending request

        Returns:
            Message sending response

        Raises:
            RobotRepositoryError: If the operation fails
        """
        pass

    @abstractmethod
    async def get_conversation_history(self, conversation_id: int) -> ConversationHistoryResponse:
        """
        Get conversation history.

        Args:
            conversation_id: Target conversation ID

        Returns:
            Conversation history response

        Raises:
            RobotRepositoryError: If the operation fails
        """
        pass


class RobotRepositoryError(Exception):
    """Base exception for robot repository operations."""

    def __init__(self, message: str, status_code: int | None = None, details: dict | None = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}


class RobotAuthenticationError(RobotRepositoryError):
    """Raised when authentication fails."""

    pass


class RobotNotFoundError(RobotRepositoryError):
    """Raised when a requested resource is not found."""

    pass


class RobotValidationError(RobotRepositoryError):
    """Raised when request validation fails."""

    pass


class RobotNetworkError(RobotRepositoryError):
    """Raised when network communication fails."""

    pass
