"""Chat client interfaces - Domain layer contracts."""

from abc import ABC, abstractmethod

from src.external.robot.model import (
    AddMembersRequest,
    AddMembersResponse,
    ConversationHistoryResponse,
    ConversationResponse,
    CreateConversationRequest,
    SendMessageRequest,
    SendMessageResponse,
    UpdateConversationRequest,
    UserCredentialsRequest,
    UserCredentialsResponse,
)


class ChatClient(ABC):
    """
    Abstract client for chat conversation operations.

    This interface defines the contract for communicating with external chat services,
    following Clean Architecture principles by keeping the domain layer
    independent of external frameworks and infrastructure.

    The client handles:
    - User authentication and authorization
    - Conversation lifecycle management (create, update, add members)
    - Message operations (send, retrieve history)
    """

    @abstractmethod
    async def get_user_credentials(
        self, request: UserCredentialsRequest, said_token: str | None = None
    ) -> UserCredentialsResponse:
        """
        Get user authorization credentials for chat services.

        Args:
            request: User credentials request
            said_token: Optional SAID token for Client-Assertion header

        Returns:
            User credentials response with service tokens

        Raises:
            ChatClientError: If the operation fails
        """
        pass

    @abstractmethod
    async def create_conversation(self, request: CreateConversationRequest) -> ConversationResponse:
        """
        Create a new chat conversation.

        Args:
            request: Conversation creation request

        Returns:
            Created conversation response

        Raises:
            ChatClientError: If the operation fails
        """
        pass

    @abstractmethod
    async def add_conversation_members(self, conversation_id: int, request: AddMembersRequest) -> AddMembersResponse:
        """
        Add members to an existing chat conversation.

        Args:
            conversation_id: Target conversation ID
            request: Members addition request

        Returns:
            Members addition response

        Raises:
            ChatClientError: If the operation fails
        """
        pass

    @abstractmethod
    async def update_conversation(
        self, conversation_id: int, request: UpdateConversationRequest
    ) -> ConversationResponse:
        """
        Update chat conversation properties.

        Args:
            conversation_id: Target conversation ID
            request: Conversation update request

        Returns:
            Updated conversation response

        Raises:
            ChatClientError: If the operation fails
        """
        pass

    @abstractmethod
    async def send_message(self, conversation_id: int, request: SendMessageRequest) -> SendMessageResponse:
        """
        Send a message to a chat conversation.

        Args:
            conversation_id: Target conversation ID
            request: Message sending request

        Returns:
            Message sending response

        Raises:
            ChatClientError: If the operation fails
        """
        pass

    @abstractmethod
    async def get_conversation_history(self, conversation_id: int) -> ConversationHistoryResponse:
        """
        Get chat conversation history.

        Args:
            conversation_id: Target conversation ID

        Returns:
            Conversation history response

        Raises:
            ChatClientError: If the operation fails
        """
        pass


class ChatClientError(Exception):
    """Base exception for chat client operations."""

    def __init__(self, message: str, status_code: int | None = None, details: dict | None = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}


class ChatAuthenticationError(ChatClientError):
    """Raised when authentication fails."""

    pass


class ChatNotFoundError(ChatClientError):
    """Raised when a requested resource is not found."""

    pass


class ChatValidationError(ChatClientError):
    """Raised when request validation fails."""

    pass


class ChatNetworkError(ChatClientError):
    """Raised when network communication fails."""

    pass
