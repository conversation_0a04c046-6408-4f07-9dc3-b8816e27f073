""" Conversation interfaces - Domain layer contracts."""

from abc import ABC, abstractmethod

from src.external.robot.model import (
    AddMembersRequest,
    AddMembersResponse,
    ConversationHistoryResponse,
    ConversationResponse,
    CreateConversationRequest,
    SendMessageRequest,
    SendMessageResponse,
    UpdateConversationRequest,
    UserCredentialsRequest,
    UserCredentialsResponse,
)


class Conversation(ABC):
    """
    Abstract client for chat conversation operations.

    This interface defines the contract for communicating with external chat services,
    following Clean Architecture principles by keeping the domain layer
    independent of external frameworks and infrastructure.

    The client handles:
    - User authentication and authorization
    - Conversation lifecycle management (create, update, add members)
    - Message operations (send, retrieve history)
    """

    @abstractmethod
    async def get_user_credentials(
        self, request: UserCredentialsRequest, said_token: str | None = None
    ) -> UserCredentialsResponse:
        """
        Get user authorization credentials for chat services.

        Args:
            request: User credentials request
            said_token: Optional SAID token for Client-Assertion header

        Returns:
            User credentials response with service tokens

        Raises:
            ConversationError: If the operation fails
        """
        pass

    @abstractmethod
    async def create_conversation(self, request: CreateConversationRequest) -> ConversationResponse:
        """
        Create a new chat conversation.

        Args:
            request: Conversation creation request

        Returns:
            Created conversation response

        Raises:
            ConversationError: If the operation fails
        """
        pass

    @abstractmethod
    async def add_conversation_members(self, conversation_id: int, request: AddMembersRequest) -> AddMembersResponse:
        """
        Add members to an existing chat conversation.

        Args:
            conversation_id: Target conversation ID
            request: Members addition request

        Returns:
            Members addition response

        Raises:
            ConversationError: If the operation fails
        """
        pass

    @abstractmethod
    async def update_conversation(
        self, conversation_id: int, request: UpdateConversationRequest
    ) -> ConversationResponse:
        """
        Update chat conversation properties.

        Args:
            conversation_id: Target conversation ID
            request: Conversation update request

        Returns:
            Updated conversation response

        Raises:
            ConversationError: If the operation fails
        """
        pass

    @abstractmethod
    async def send_message(self, conversation_id: int, request: SendMessageRequest) -> SendMessageResponse:
        """
        Send a message to a chat conversation.

        Args:
            conversation_id: Target conversation ID
            request: Message sending request

        Returns:
            Message sending response

        Raises:
            ConversationError: If the operation fails
        """
        pass

    @abstractmethod
    async def get_conversation_history(self, conversation_id: int) -> ConversationHistoryResponse:
        """
        Get chat conversation history.

        Args:
            conversation_id: Target conversation ID

        Returns:
            Conversation history response

        Raises:
            ConversationError: If the operation fails
        """
        pass
