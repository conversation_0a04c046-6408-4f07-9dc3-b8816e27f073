"""
Robot client package - Clean Architecture implementation for robot_conversation operations.

This package provides a clean, well-structured interface for robot_conversation operations
following Clean Architecture principles with proper separation of concerns.

Usage:
    from src.external.robot_conversation import get_robot_service

    robot_service = get_robot_service()

    # Get user authorization
    response = await robot_service.get_user_authorization(
        user_id="jingxuyang",
        service_ids_with_scopes={
            "service1": ["read", "write"],
            "service2": ["admin"]
        }
    )

    # Create conversation
    conversation = await robot_service.create_conversation(
        title="Support Session",
        ids=["user1", "user2"]
    )
"""

from .factory import create_robot_service, get_robot_service

# Export main interfaces
__all__ = [
    "get_robot_service",
    "create_robot_service",
]
