"""Robot service factory - Dependency injection and configuration."""

import logging

from src.external.idaas.client import IDaaSClient
from src.usecases.robot_conversation.robot_conversation_service import RobotConversationService

from .http_client import RobotHttpClient
from ...config import settings

logger = logging.getLogger(__name__)


class RobotConversationServiceFactory:
    """
    Factory for creating robot_conversation service instances with proper dependency injection.

    This factory handles the creation and configuration of robot_conversation services,
    ensuring proper initialization of dependencies and following Clean Architecture
    principles by managing the dependency graph.
    """

    _robot_service: RobotConversationService | None = None
    _default_robot_service: RobotConversationService | None = None

    @classmethod
    def create_robot_service(
        cls, base_url: str | None = None, timeout: float | None = None, force_recreate: bool = False
    ) -> RobotConversationService:
        """
        Create or return cached robot_conversation service instance.

        Args:
            base_url: Override default robot_conversation API base URL
            timeout: Override default request timeout
            force_recreate: Force creation of new instance

        Returns:
            Configured robot_conversation service instance
        """
        if cls._robot_service is None or force_recreate:
            cls._robot_service = cls._build_robot_service(base_url, timeout)

        return cls._robot_service

    @classmethod
    def _build_robot_service(cls, base_url: str | None = None, timeout: float | None = None) -> RobotConversationService:
        """
        Build robot_conversation service with all dependencies.

        Args:
            base_url: Robot API base URL
            timeout: Request timeout

        Returns:
            Configured robot_conversation service instance
        """
        # Create and configure IDaaS client
        idaas_client = IDaaSClient()
        idaas_client.initialize(
            client_id=settings.idaas_dhagent_client_id,
            client_secret=settings.idaas_dhagent_client_secret,
            service_id=settings.idaas_robot_service_id,
            endpoint=settings.idaas_host,
            scopes=settings.idaas_robot_service_scopes,
        )

        # Create HTTP client (chat client implementation)
        api_base_url = base_url or settings.robot_api_base_url
        api_timeout = timeout or settings.robot_api_timeout

        chat_client = RobotHttpClient(base_url=api_base_url, idaas_client=idaas_client, timeout=api_timeout)

        # Create and return service
        robot_service = RobotConversationService(chat_client)

        logger.info(f"Created robot_conversation service with base URL: {api_base_url}")
        return robot_service

    @classmethod
    def get_default_robot_service(cls) -> RobotConversationService:
        """
        Get or create the default robot_conversation service instance.

        Returns:
            Default robot_conversation service instance
        """
        if cls._default_robot_service is None:
            cls._default_robot_service = cls.create_robot_service()
        return cls._default_robot_service

    @classmethod
    def reset(cls) -> None:
        """Reset factory state (useful for testing)."""
        cls._robot_service = None
        cls._default_robot_service = None


def get_robot_service() -> RobotConversationService:
    """
    Get the default robot_conversation service instance.

    Returns:
        Default robot_conversation service instance
    """
    return RobotConversationServiceFactory.get_default_robot_service()


def create_robot_service(base_url: str | None = None, timeout: float | None = None) -> RobotConversationService:
    """
    Create a new robot_conversation service instance with custom configuration.

    Args:
        base_url: Custom robot_conversation API base URL
        timeout: Custom request timeout

    Returns:
        New robot_conversation service instance
    """
    return RobotConversationServiceFactory.create_robot_service(base_url=base_url, timeout=timeout, force_recreate=True)
