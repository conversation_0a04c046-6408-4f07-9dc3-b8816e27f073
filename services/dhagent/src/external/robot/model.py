"""Robot domain entities - Core business logic for robot_conversation operations."""

from enum import Enum
from typing import Any

from pydantic import BaseModel, Field


class SearchType(Enum):
    """User search type for robot_conversation operations."""

    LDAP_NAME = "ldap_name"
    FEISHU_USER_ID = "feishu_user_id"


class IdType(Enum):
    """Member ID type for conversation operations."""

    LDAP_NAME = "ldap_name"
    FEISHU_USER_ID = "feishu_user_id"


class MessageRole(Enum):
    """Message sender/recipient roles."""

    USER = "user"
    AGENT = "agent"
    ASSISTANT = "assistant"
    NOTIFIER = "notifier"


class MessageStatus(Enum):
    """Message processing status."""

    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    FINISHED = "FINISHED"
    FAILED = "FAILED"


class ServiceCredential(BaseModel):
    """Service credential information."""

    id: str = Field(..., description="Service ID")
    scopes: list[str] = Field(..., description="Required scopes for the service")


class ServiceToken(BaseModel):
    """Service access token result."""

    id: str = Field(..., description="Service ID")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    access_token: str = Field(..., description="Access token")
    error: str | None = Field(None, description="Error message if token retrieval failed")


class MemberInfo(BaseModel):
    id_type: str = Field(..., description="ID type")
    ids: list[str] = Field(..., description="List of IDs")


class ConversationMetadata(BaseModel):
    """Conversation metadata."""

    members: MemberInfo = Field(..., description="Member information")
    feedback: Any | None = Field(None, description="Feedback information")


class Conversation(BaseModel):
    """Robot conversation entity."""

    id: int = Field(..., description="Conversation ID")
    title: str = Field(..., description="Conversation title")
    chat_id: str = Field(..., description="Chat ID for external systems")
    user_ldap_name: str = Field(..., description="User LDAP name")
    primary_assistant_name: str = Field(..., description="Primary assistant name")
    primary_assistant_display_name: str = Field(..., description="Primary assistant display name")
    metadata: ConversationMetadata = Field(..., description="Conversation metadata")
    create_time: float = Field(..., description="Creation timestamp")
    update_time: float = Field(..., description="Update timestamp")


class MessageSender(BaseModel):
    """Message sender information."""

    role: MessageRole = Field(..., description="Sender role")
    name: str = Field(..., description="Sender name")
    display_name: str | None = Field(None, description="Sender display name")


class MessageRecipient(BaseModel):
    """Message recipient information."""

    role: MessageRole = Field(..., description="Recipient role")
    name: str = Field(..., description="Recipient name")
    display_name: str | None = Field(None, description="Recipient display name")


class MessageContent(BaseModel):
    """Message content part."""

    mime_type: str = Field(..., description="Content MIME type")
    body: dict[str, Any] = Field(..., description="Content body")


class MessageMetadata(BaseModel):
    """Message metadata."""

    server: dict[str, Any] = Field(default_factory=dict, description="Server metadata")
    client: dict[str, Any] = Field(default_factory=dict, description="Client metadata")
    user: dict[str, Any] = Field(default_factory=dict, description="User metadata")


class Message(BaseModel):
    """Robot message entity."""

    id: int = Field(..., description="Message ID")
    parent_id: int | None = Field(None, description="Parent message ID")
    sender: MessageSender = Field(..., description="Message sender")
    recipient: MessageRecipient = Field(..., description="Message recipient")
    contents: list[MessageContent] = Field(..., description="Message contents")
    status: MessageStatus = Field(..., description="Message status")
    end_turn: bool = Field(..., description="Whether this message ends the turn")
    metadata: MessageMetadata = Field(default_factory=MessageMetadata, description="Message metadata")
    create_time: float = Field(..., description="Creation timestamp")
    update_time: float = Field(..., description="Update timestamp")


class ConversationNode(BaseModel):
    """Conversation history node."""

    id: int = Field(..., description="Node ID")
    message: Message = Field(..., description="Message content")
    parent: int | None = Field(None, description="Parent node ID")
    children: list[int] = Field(default_factory=list, description="Child node IDs")


class UserCredentialsRequest(BaseModel):
    """Request for user credentials."""

    search_type: SearchType = Field(SearchType.LDAP_NAME, description="User search type")
    user_id: str = Field(..., description="User ID")
    services: list[ServiceCredential] = Field(..., description="Required services and scopes")


class CreateConversationRequest(BaseModel):
    """Request to create a new conversation."""

    title: str = Field(..., description="Conversation title")
    scenario: str = Field("deepheal", description="Conversation scenario")
    metadata: dict[str, Any] = Field(..., description="Conversation metadata including members")


class AddMembersRequest(BaseModel):
    """Request to add members to conversation."""

    id_type: IdType = Field(IdType.LDAP_NAME, description="Member ID type")
    ids: list[str] = Field(..., description="Member IDs to add")


class UpdateConversationRequest(BaseModel):
    """Request to update conversation."""

    title: str = Field(..., description="New conversation title")


class SendMessageRequest(BaseModel):
    """Request to send a message."""

    parent_id: int | None = Field(None, description="Parent message ID")
    messages: list[dict[str, Any]] = Field(..., description="Messages to send")


# Response models
class UserCredentialsResponse(BaseModel):
    """Response for user credentials request."""

    code: int = Field(..., description="Response code")
    msg: str = Field(..., description="Response message")
    data: dict[str, list[ServiceToken]] = Field(..., description="Service tokens")


class ConversationResponse(BaseModel):
    """Response for conversation operations."""

    code: int = Field(..., description="Response code")
    msg: str = Field(..., description="Response message")
    data: Conversation = Field(..., description="Conversation data")


class AddMembersResponse(BaseModel):
    """Response for adding members."""

    code: int = Field(..., description="Response code")
    msg: str = Field(..., description="Response message")
    data: dict[str, list[str]] = Field(..., description="Invalid member IDs")


class ConversationHistoryResponse(BaseModel):
    """Response for conversation history."""

    code: int = Field(..., description="Response code")
    msg: str = Field(..., description="Response message")
    data: dict[str, Any] = Field(..., description="Conversation history")


class SendMessageResponse(BaseModel):
    """Response for sending message."""

    code: int = Field(..., description="Response code")
    msg: str = Field(..., description="Response message")
    data: dict[str, Any] = Field(..., description="Message response data")
