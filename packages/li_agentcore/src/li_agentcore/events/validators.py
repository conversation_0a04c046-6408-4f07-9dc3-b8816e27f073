"""Validation helpers for AgentEvent types and hierarchy.

- Enforces reserved and client-suffixed type rules
- Provides hierarchy normalization for parent/root/id
"""

from __future__ import annotations

import uuid
from typing import Any

from .agent_event_types import AgentEventType, ClientType


class EventTypeValidator:
    @staticmethod
    def validate_event_type(event_type: str) -> bool:
        if not event_type or not isinstance(event_type, str):
            raise ValueError("Event type must be a non-empty string")
        if AgentEventType.is_predefined_complete_type(event_type):
            return True
        for complete in AgentEventType.get_complete_types():
            if event_type.startswith(f"{complete}."):
                raise ValueError(f"Event type '{event_type}' must be exactly '{complete}' without client suffix")
        if AgentEventType.requires_client(event_type):
            raise ValueError(
                f"Event type '{event_type}' requires client specification. "
                f"Use format '{event_type}.{{client}}' where client is one of: "
                f"{', '.join(ClientType.get_all_clients())}"
            )
        for base_type in AgentEventType.get_client_required_types():
            if event_type.startswith(f"{base_type}."):
                client = event_type[len(base_type) + 1 :]
                if not ClientType.is_valid_client(client):
                    raise ValueError(
                        f"Invalid client '{client}' in event type '{event_type}'. "
                        f"Valid clients are: {', '.join(ClientType.get_all_clients())}"
                    )
                return True
        return True

    @staticmethod
    def build_client_event_type(base_type: str, client: str) -> str:
        if not AgentEventType.requires_client(base_type):
            raise ValueError(f"Event type '{base_type}' does not require client specification")
        if not ClientType.is_valid_client(client):
            raise ValueError(f"Invalid client '{client}'. Valid clients are: {', '.join(ClientType.get_all_clients())}")
        return f"{base_type}.{client}"


def validate_hierarchy(values: dict[str, Any]) -> dict[str, Any]:
    if not isinstance(values, dict):
        return values

    parentid = values.get("parentid", "")
    rootid = values.get("rootid")
    event_id = values.get("id")

    values.setdefault("source", "li-agentcore.dhagent")
    values.setdefault("type", "agent.authorize.robot_conversation")

    event_type = values.get("type")
    if event_type:
        EventTypeValidator.validate_event_type(event_type)

    if not rootid:
        if not parentid:
            if event_id:
                values["rootid"] = event_id
            else:
                new_id = str(uuid.uuid4())
                values["id"] = new_id
                values["rootid"] = new_id
        else:
            raise ValueError(
                f"Child events (parentid='{parentid}') must provide explicit rootid. "
                "rootid should be the ID of the root event in the chain."
            )

    if parentid and not values.get("rootid"):
        raise ValueError("Child events must have both parentid and rootid")

    return values
