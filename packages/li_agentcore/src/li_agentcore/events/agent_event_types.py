"""
Event type constants and validation utilities for AgentEvent
"""


class ClientType:
    """
    Valid client types for agent events that require client specification.

    These constants define the supported client platforms that can interact
    with the agent system.
    """

    ROBOT = "robot_conversation"
    CLI = "cli"
    WEB = "web"

    @classmethod
    def get_all_clients(cls) -> set[str]:
        """Get all valid client type values"""
        return {cls.ROBOT, cls.CLI, cls.WEB}

    @classmethod
    def is_valid_client(cls, client: str) -> bool:
        """Check if a client type is valid"""
        return client in cls.get_all_clients()


class AgentEventType:
    """
    Predefined agent event types for common use cases.
    Some require client suffix; others are complete.
    """

    USER_SEND = "user.send"
    AGENT_INVOKE = "agent.invoke"
    AGENT_INTERRUPT = "agent.interrupt"
    USER_RESUME = "user.resume"
    AGENT_AUTHORIZE = "agent.authorize"
    USER_GRANT = "user.grant"

    @classmethod
    def get_client_required_types(cls) -> set[str]:
        return {
            cls.AGENT_INTERRUPT,
            cls.USER_RESUME,
            cls.AGENT_AUTHORIZE,
            cls.USER_GRANT,
        }

    @classmethod
    def get_complete_types(cls) -> set[str]:
        return {cls.USER_SEND, cls.AGENT_INVOKE}

    @classmethod
    def requires_client(cls, event_type: str) -> bool:
        return event_type in cls.get_client_required_types()

    @classmethod
    def is_predefined_complete_type(cls, event_type: str) -> bool:
        return event_type in cls.get_complete_types()
